import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON> } from "@/components/hero"
import { Features } from "@/components/features"
import { Services } from "@/components/services"
import { Stats } from "@/components/stats"
import { SuccessStories } from "@/components/success-stories"
import { Testimonials } from "@/components/testimonials"
import { CTA } from "@/components/cta"
import { Footer } from "@/components/footer"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/sonner"

function App() {
  return (
    <ThemeProvider defaultTheme="light" storageKey="gradnext-theme">
      <div className="min-h-screen bg-background">
        <Header />
        <main>
          <Hero />
          <Features />
          <Services />
          <Stats />
          <SuccessStories />
          <Testimonials />
          <CTA />
        </main>
        <Footer />
        <Toaster />
      </div>
    </ThemeProvider>
  )
}

export default App
