import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, User, Brain, ArrowRight, Clock, Target, BookOpen } from "lucide-react"

const services = [
  {
    icon: User,
    title: "1:1 Coaching Program",
    description: "Tailored 1:1 program for personalized preparation with expert consultants",
    features: ["Personal mentor from MBB", "Custom learning path", "Mock interviews", "Case practice"],
    price: "From $299",
    badge: "Most Popular",
    color: "blue",
  },
  {
    icon: Users,
    title: "Consulting Cohort 101",
    description: "Consulting foundations with global like-minded peers in structured cohorts",
    features: ["Group learning", "Peer networking", "Weekly sessions", "Case competitions"],
    price: "From $199",
    badge: "Best Value",
    color: "green",
  },
  {
    icon: Brain,
    title: "CaseBuddy",
    description: "Self practice cases at your own pace with AI-powered feedback",
    features: ["100+ case studies", "AI feedback", "Progress tracking", "24/7 access"],
    price: "From $99",
    badge: "Self-Paced",
    color: "purple",
  },
]

export function Services() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Choose Your Path to Success</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Whether you prefer personalized coaching, group learning, or self-paced study, we have the perfect program
            to help you land your dream consulting role.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {services.map((service, index) => (
            <Card
              key={index}
              className="relative border-2 hover:border-blue-200 transition-all duration-300 hover:shadow-xl"
            >
              {service.badge && (
                <Badge
                  className={`absolute -top-3 left-1/2 transform -translate-x-1/2 ${
                    service.color === "blue"
                      ? "bg-blue-600"
                      : service.color === "green"
                        ? "bg-green-600"
                        : "bg-purple-600"
                  }`}
                >
                  {service.badge}
                </Badge>
              )}

              <CardHeader className="text-center pb-4">
                <div
                  className={`w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    service.color === "blue"
                      ? "bg-blue-100"
                      : service.color === "green"
                        ? "bg-green-100"
                        : "bg-purple-100"
                  }`}
                >
                  <service.icon
                    className={`h-8 w-8 ${
                      service.color === "blue"
                        ? "text-blue-600"
                        : service.color === "green"
                          ? "text-green-600"
                          : "text-purple-600"
                    }`}
                  />
                </div>
                <CardTitle className="text-xl font-semibold mb-2">{service.title}</CardTitle>
                <CardDescription className="text-gray-600">{service.description}</CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                <div className="space-y-3">
                  {service.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center text-sm">
                      <div
                        className={`w-2 h-2 rounded-full mr-3 ${
                          service.color === "blue"
                            ? "bg-blue-600"
                            : service.color === "green"
                              ? "bg-green-600"
                              : "bg-purple-600"
                        }`}
                      />
                      {feature}
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4">
                  <div className="text-2xl font-bold text-gray-900 mb-4">{service.price}</div>
                  <Button
                    className={`w-full ${
                      service.color === "blue"
                        ? "bg-blue-600 hover:bg-blue-700"
                        : service.color === "green"
                          ? "bg-green-600 hover:bg-green-700"
                          : "bg-purple-600 hover:bg-purple-700"
                    }`}
                  >
                    Get Started
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <Clock className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Flexible Scheduling</h3>
            <p className="text-sm text-gray-600">Sessions available across all time zones</p>
          </div>
          <div className="text-center">
            <Target className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Proven Results</h3>
            <p className="text-sm text-gray-600">95% of our students receive offers</p>
          </div>
          <div className="text-center">
            <BookOpen className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Comprehensive Resources</h3>
            <p className="text-sm text-gray-600">Access to 500+ case studies and frameworks</p>
          </div>
        </div>
      </div>
    </section>
  )
}
