import { useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

const companies = [
  {
    name: "McKinsey & Company",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/McKinsey-Company-Logo.png",
    fallback: "/logos/mckinsey.svg",
    alt: "McKinsey & Company",
    color: "#1a1a1a",
    shortName: "McKinsey"
  },
  {
    name: "BCG",
    logo: "https://logos-world.net/wp-content/uploads/2020/06/Boston-Consulting-Group-BCG-Logo.png",
    fallback: "/logos/bcg.svg",
    alt: "Boston Consulting Group",
    color: "#00a651",
    shortName: "BCG"
  },
  {
    name: "Bain & Company",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Bain-Company-Logo.png",
    fallback: "/logos/bain.svg",
    alt: "Bain & Company",
    color: "#e31837",
    shortName: "BAIN"
  },
  {
    name: "<PERSON><PERSON>",
    logo: "https://www.kearney.com/documents/*********/*********/Kearney_logo_blue_CMYK.png",
    fallback: "/logos/kearney.svg",
    alt: "Kearney",
    color: "#0066cc",
    shortName: "Kearney"
  },
  {
    name: "Accenture",
    logo: "https://logos-world.net/wp-content/uploads/2020/07/Accenture-Logo.png",
    fallback: "/logos/accenture.svg",
    alt: "Accenture",
    color: "#a100ff",
    shortName: "Accenture"
  },
  {
    name: "EY-Parthenon",
    logo: "https://logos-world.net/wp-content/uploads/2020/06/EY-Logo.png",
    fallback: "/logos/ey.svg",
    alt: "EY-Parthenon",
    color: "#ffe600",
    shortName: "EY"
  },
  {
    name: "Arthur D. Little",
    logo: "https://www.adlittle.com/sites/default/files/adl_logo_0.png",
    fallback: "/logos/adl.svg",
    alt: "Arthur D. Little",
    color: "#1a472a",
    shortName: "ADL"
  },
  {
    name: "Oliver Wyman",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Oliver-Wyman-Logo.png",
    fallback: "/logos/oliver-wyman.svg",
    alt: "Oliver Wyman",
    color: "#0066cc",
    shortName: "Oliver Wyman"
  },
  {
    name: "Strategy&",
    logo: "https://logos-world.net/wp-content/uploads/2021/02/Strategy-Logo.png",
    fallback: "/logos/strategy.svg",
    alt: "Strategy&",
    color: "#ff6600",
    shortName: "Strategy&"
  },
  {
    name: "Deloitte",
    logo: "https://logos-world.net/wp-content/uploads/2020/06/Deloitte-Logo.png",
    fallback: "/logos/deloitte.svg",
    alt: "Deloitte",
    color: "#86bc25",
    shortName: "Deloitte"
  },
]

export function SuccessStories() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const itemsPerView = 5

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + itemsPerView >= companies.length ? 0 : prev + itemsPerView))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? Math.max(0, companies.length - itemsPerView) : Math.max(0, prev - itemsPerView),
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our clients have received offers from top consulting firms
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Join the ranks of successful consultants who landed their dream jobs with our guidance
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="icon"
              onClick={prevSlide}
              className="absolute left-0 z-10 bg-white shadow-lg"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center justify-center space-x-8 mx-16 overflow-hidden">
              {companies.slice(currentIndex, currentIndex + itemsPerView).map((company, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 flex items-center justify-center h-16 w-32 bg-white rounded-lg hover:bg-gray-50 transition-colors border border-gray-100 shadow-sm p-3"
                >
                  <img
                    src={company.logo}
                    alt={company.alt}
                    className="max-h-full max-w-full object-contain filter grayscale hover:grayscale-0 transition-all duration-300"
                    onError={(e) => {
                      // Try fallback logo first, then show company name
                      if (company.fallback && e.currentTarget.src !== company.fallback) {
                        e.currentTarget.src = company.fallback;
                      } else {
                        e.currentTarget.style.display = 'none';
                        const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                        if (nextElement) {
                          nextElement.style.display = 'block';
                        }
                      }
                    }}
                  />
                  <span
                    className="font-bold text-xs hidden"
                    style={{ color: company.color }}
                  >
                    {company.shortName}
                  </span>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              size="icon"
              onClick={nextSlide}
              className="absolute right-0 z-10 bg-white shadow-lg"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Dots indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: Math.ceil(companies.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  Math.floor(currentIndex / itemsPerView) === index ? "bg-blue-600" : "bg-gray-300"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
