"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

const companies = [
  { name: "McKinsey & Company", logo: "MC" },
  { name: "BCG", logo: "BCG" },
  { name: "Bain & Company", logo: "BAIN" },
  { name: "<PERSON><PERSON>", logo: "KEARNEY" },
  { name: "Accenture", logo: "ACN" },
  { name: "EY-Parthenon", logo: "EY" },
  { name: "<PERSON>", logo: "ADL" },
  { name: "<PERSON>", logo: "OW" },
  { name: "Strategy&", logo: "S&" },
  { name: "Deloitte", logo: "DTT" },
]

export function SuccessStories() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const itemsPerView = 5

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + itemsPerView >= companies.length ? 0 : prev + itemsPerView))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? Math.max(0, companies.length - itemsPerView) : Math.max(0, prev - itemsPerView),
    )
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our clients have received offers from top consulting firms
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Join the ranks of successful consultants who landed their dream jobs with our guidance
          </p>
        </div>

        <div className="relative max-w-5xl mx-auto">
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="icon"
              onClick={prevSlide}
              className="absolute left-0 z-10 bg-white shadow-lg"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center justify-center space-x-8 mx-16 overflow-hidden">
              {companies.slice(currentIndex, currentIndex + itemsPerView).map((company, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 flex items-center justify-center h-16 w-32 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <span className="font-bold text-gray-700 text-sm">{company.logo}</span>
                </div>
              ))}
            </div>

            <Button
              variant="outline"
              size="icon"
              onClick={nextSlide}
              className="absolute right-0 z-10 bg-white shadow-lg"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Dots indicator */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: Math.ceil(companies.length / itemsPerView) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index * itemsPerView)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  Math.floor(currentIndex / itemsPerView) === index ? "bg-blue-600" : "bg-gray-300"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
