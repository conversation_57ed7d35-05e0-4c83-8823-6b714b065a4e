import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { BookOpen, Users, Target, Briefcase, Award, TrendingUp } from "lucide-react"

const features = [
  {
    icon: BookOpen,
    title: "Personalized Learning Paths",
    description: "Get customized curriculum based on your career goals, current skills, and industry demands.",
  },
  {
    icon: Users,
    title: "Industry Mentorship",
    description: "Connect with experienced professionals who provide guidance and insider knowledge.",
  },
  {
    icon: Target,
    title: "Career Matching",
    description: "AI-powered matching system connects you with opportunities that fit your profile perfectly.",
  },
  {
    icon: Briefcase,
    title: "Real-World Projects",
    description: "Build your portfolio with projects that solve actual business problems and showcase your skills.",
  },
  {
    icon: Award,
    title: "Industry Certifications",
    description: "Earn recognized certifications that validate your expertise to potential employers.",
  },
  {
    icon: TrendingUp,
    title: "Career Analytics",
    description: "Track your progress with detailed analytics and insights into your career development.",
  },
]

export function Features() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Everything You Need to Launch Your Career
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Our comprehensive platform provides all the tools, resources, and support you need to transition from
            student to successful professional.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <CardTitle className="text-xl font-semibold">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 leading-relaxed">{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
