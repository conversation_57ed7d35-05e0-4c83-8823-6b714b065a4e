import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Calendar, Users, Award } from "lucide-react"

export function CTA() {
  return (
    <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-700">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center text-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Land Your Dream Consulting Job?</h2>
          <p className="text-xl opacity-90 mb-12 max-w-2xl mx-auto">
            Join thousands of successful consultants who chose gradnext to accelerate their careers. Your journey to MBB
            starts here.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="flex flex-col items-center">
              <Calendar className="h-12 w-12 mb-4 opacity-80" />
              <h3 className="font-semibold mb-2">Book a Free Consultation</h3>
              <p className="text-sm opacity-75">Discuss your goals with our experts</p>
            </div>
            <div className="flex flex-col items-center">
              <Users className="h-12 w-12 mb-4 opacity-80" />
              <h3 className="font-semibold mb-2">Join Our Community</h3>
              <p className="text-sm opacity-75">Connect with like-minded peers</p>
            </div>
            <div className="flex flex-col items-center">
              <Award className="h-12 w-12 mb-4 opacity-80" />
              <h3 className="font-semibold mb-2">Land Your Offer</h3>
              <p className="text-sm opacity-75">Secure your dream consulting role</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold">
              Book Free Consultation
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg bg-transparent"
            >
              Explore Programs
            </Button>
          </div>

          <div className="mt-8 text-sm opacity-75">
            ✓ No commitment required • ✓ 30-day money-back guarantee • ✓ Trusted by 2,000+ students
          </div>
        </div>
      </div>
    </section>
  )
}
