import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON>, Quote } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON>",
    role: "Consultant at McKinsey & Company",
    image: "/placeholder.svg?height=60&width=60",
    content:
      "The 1:1 coaching was game-changing. My coach helped me structure my thinking and gave me the confidence to tackle any case. I received offers from both McKinsey and BCG!",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Associate at Bain & Company",
    image: "/placeholder.svg?height=60&width=60",
    content:
      "The Consulting Cohort 101 program was incredible. Learning alongside peers from around the world and getting feedback from actual MBB consultants made all the difference.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Consultant at BCG",
    image: "/placeholder.svg?height=60&width=60",
    content:
      "<PERSON><PERSON><PERSON><PERSON> helped me practice cases anytime, anywhere. The AI feedback was surprisingly accurate and helped me identify my weak spots. Highly recommend!",
    rating: 5,
  },
]

export function Testimonials() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Success Stories from Our Community</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Hear from consultants who transformed their careers with gradnext
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 relative">
              <CardContent className="p-8">
                <Quote className="h-8 w-8 text-blue-600 mb-4 opacity-50" />

                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>

                <p className="text-gray-700 mb-6 leading-relaxed italic">"{testimonial.content}"</p>

                <div className="flex items-center">
                  <img
                    src={testimonial.image || "/placeholder.svg"}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full mr-4"
                  />
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.role}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
