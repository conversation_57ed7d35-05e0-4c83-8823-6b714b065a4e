export function Stats() {
  const stats = [
    { number: "2,000+", label: "Students Coached", color: "text-blue-600" },
    { number: "95%", label: "Success Rate", color: "text-green-600" },
    { number: "500+", label: "Offers Secured", color: "text-purple-600" },
    { number: "50+", label: "Expert Coaches", color: "text-orange-600" },
  ]

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Impact in Numbers</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Join thousands of successful consultants who trusted gradnext to launch their careers
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className={`text-4xl md:text-5xl font-bold ${stat.color} mb-2`}>{stat.number}</div>
              <div className="text-gray-600 font-medium">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
